{"__meta": {"id": "X53c86303a3ae5ca3da4e36f3841add2c", "datetime": "2025-06-10 16:39:05", "utime": **********.983528, "method": "GET", "uri": "/klozza/public/_ignition/health-check", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 5, "messages": [{"message": "[16:39:05] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": **********.666051, "collector": "log"}, {"message": "[16:39:05] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": **********.666121, "collector": "log"}, {"message": "[16:39:05] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.679756, "collector": "log"}, {"message": "[16:39:05] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": **********.691927, "collector": "log"}, {"message": "[16:39:05] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": **********.692039, "collector": "log"}]}, "time": {"start": **********.882119, "end": **********.983568, "duration": 1.1014490127563477, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": **********.882119, "relative_start": 0, "end": **********.647527, "relative_end": **********.647527, "duration": 0.7654080390930176, "duration_str": "765ms", "params": [], "collector": null}, {"label": "Application", "start": **********.65339, "relative_start": 0.7712709903717041, "end": **********.98357, "relative_end": 2.1457672119140625e-06, "duration": 0.33018016815185547, "duration_str": "330ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 29832080, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET _ignition/health-check", "middleware": "Facade\\Ignition\\Http\\Middleware\\IgnitionEnabled", "uses": "Facade\\Ignition\\Http\\Controllers\\HealthCheckController@__invoke", "controller": "Facade\\Ignition\\Http\\Controllers\\HealthCheckController", "as": "ignition.healthCheck", "namespace": null, "prefix": "_ignition", "where": []}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/_ignition/health-check", "status_code": "<pre class=sf-dump id=sf-dump-820109569 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-820109569\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-858197137 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-858197137\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-470153930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-470153930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-294923805 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/klozza/public/test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2533 characters\">__clerk_db_jwt=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __clerk_db_jwt_2UYcsRFZ=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __refresh_2UYcsRFZ=rCX9HMTMzF9QXwDy1HwQ; __session=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.gMlP5bpPsF9RGYYHPqpZPdKMjX48ub7CTAL8fM7Q-6J2oJ4KZumNx5nS9cfbLcAKH8moi64uUoQcx97Dz1oUujg0IUFkINCBdDZBl0HZZZ4njPXYOtcKuJL9Cxc6qrAFDURt2De1ddeTiIN9sHv6MjGQC7DloeW4osjraD82gIxK3AC_nY9DvuvVL1HM4Z6kXKLUQMhNKiz_ZbYcUghN0XqPommeW5u1WbgRHO61-ff4j09Sy88mk9ibiptyBBm_AXnp7cb4fz9tU9ddmh4KlvcaVIqWPh1r4fvWkJLB6FR9LxBRSnSxcc9HKngi7bmmoWbuaJG4o9-dN6mzn8ea3w; __client_uat_2UYcsRFZ=1746877532; __client_uat=1746877532; __session_2UYcsRFZ=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJzcmYiOnRydWUsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.WXRZ7RQEUvPhycp89hRZRKiCRcxaGKJdpTkfAZKF0jKvnn6E9iUTJoOxFxXy8N_3thT5lSXhRD8feEqYUv0j6a6OPv3lDCPzlniS2A73uy3JasqJ-zent4jZlyHAdSkUrhC8Ez01YSm6UZJTtAZzNBtPw_-fcv21-FxfEYwR8BkdHLGpALpLrCcDHJ5-i1zUIjH8_ORuh0aS-N2cjLZEOJ6ehGNAEX15fiRogbHtItp8_0xK8He6-AKwL-vH3GlK1huFox1ByyDE8A7FPhXrGtsq5hy1a39czoFcqX-ZATp8f0XQmnoHeCH8DHY_eKYXt5vpUuIOgf-_3D4VW-7GLA; XSRF-TOKEN=eyJpdiI6IkJaZTdjK1NreFQvdkNUSG85NTFITFE9PSIsInZhbHVlIjoicjV0QnBLSGVmVnY2OGcva2tqREtiVHE5dGJVc3BVd3NVYTJ0MWlucGhzRTNhRzdJRlFBSkZQdTV4L0htSzF2c0YyeGdtU0Y5clRtc0J5MklJRnlyN2RUR1RXZ1V2bFlQSWRiOEloYloxOXQ5MTcrcGNQRnFUdXZzMmdZdDdOcHEiLCJtYWMiOiI3YTY4Y2Q1MjM2MTM2MWQ0OTRlYTFjOTQyMzcxMmRhNmIxY2M0YmE3ZDQwZWM2MWQ0NTc0YTliNGRlNDA5MjA4IiwidGFnIjoiIn0%3D; klozza_session=eyJpdiI6Imx3NGV4K3l1VC9jNld0UWtVVTRNd3c9PSIsInZhbHVlIjoicEMyb1dVcDRvRWVoUWpTb1UrYllCaEdzUDdyVjU1Qy9IYTZWS25JWDNzc3R5ZTl6MEZLQWNEMEFJUjNueEpUNTdoR3I2M2lwSkdFU2NSNWEzWjkrZktSNnMzQW0yejJmM1N1ZVVVNEwya1BhbDF3TEE4STR3K1p2ZUxBSnhoWHAiLCJtYWMiOiIwZjEwOGExMTk3OWRjYWRiZmM4YThkMDhhZWFjODg2YTRhMmFkZmMyMDM0MTliYWMyMzA3OTA2YzI1ZjQwN2ZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294923805\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-629034456 data-indent-pad=\"  \"><span class=sf-dump-note>array:55</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/klozza/public/test</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2533 characters\">__clerk_db_jwt=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __clerk_db_jwt_2UYcsRFZ=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __refresh_2UYcsRFZ=rCX9HMTMzF9QXwDy1HwQ; __session=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.gMlP5bpPsF9RGYYHPqpZPdKMjX48ub7CTAL8fM7Q-6J2oJ4KZumNx5nS9cfbLcAKH8moi64uUoQcx97Dz1oUujg0IUFkINCBdDZBl0HZZZ4njPXYOtcKuJL9Cxc6qrAFDURt2De1ddeTiIN9sHv6MjGQC7DloeW4osjraD82gIxK3AC_nY9DvuvVL1HM4Z6kXKLUQMhNKiz_ZbYcUghN0XqPommeW5u1WbgRHO61-ff4j09Sy88mk9ibiptyBBm_AXnp7cb4fz9tU9ddmh4KlvcaVIqWPh1r4fvWkJLB6FR9LxBRSnSxcc9HKngi7bmmoWbuaJG4o9-dN6mzn8ea3w; __client_uat_2UYcsRFZ=1746877532; __client_uat=1746877532; __session_2UYcsRFZ=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJzcmYiOnRydWUsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.WXRZ7RQEUvPhycp89hRZRKiCRcxaGKJdpTkfAZKF0jKvnn6E9iUTJoOxFxXy8N_3thT5lSXhRD8feEqYUv0j6a6OPv3lDCPzlniS2A73uy3JasqJ-zent4jZlyHAdSkUrhC8Ez01YSm6UZJTtAZzNBtPw_-fcv21-FxfEYwR8BkdHLGpALpLrCcDHJ5-i1zUIjH8_ORuh0aS-N2cjLZEOJ6ehGNAEX15fiRogbHtItp8_0xK8He6-AKwL-vH3GlK1huFox1ByyDE8A7FPhXrGtsq5hy1a39czoFcqX-ZATp8f0XQmnoHeCH8DHY_eKYXt5vpUuIOgf-_3D4VW-7GLA; XSRF-TOKEN=eyJpdiI6IkJaZTdjK1NreFQvdkNUSG85NTFITFE9PSIsInZhbHVlIjoicjV0QnBLSGVmVnY2OGcva2tqREtiVHE5dGJVc3BVd3NVYTJ0MWlucGhzRTNhRzdJRlFBSkZQdTV4L0htSzF2c0YyeGdtU0Y5clRtc0J5MklJRnlyN2RUR1RXZ1V2bFlQSWRiOEloYloxOXQ5MTcrcGNQRnFUdXZzMmdZdDdOcHEiLCJtYWMiOiI3YTY4Y2Q1MjM2MTM2MWQ0OTRlYTFjOTQyMzcxMmRhNmIxY2M0YmE3ZDQwZWM2MWQ0NTc0YTliNGRlNDA5MjA4IiwidGFnIjoiIn0%3D; klozza_session=eyJpdiI6Imx3NGV4K3l1VC9jNld0UWtVVTRNd3c9PSIsInZhbHVlIjoicEMyb1dVcDRvRWVoUWpTb1UrYllCaEdzUDdyVjU1Qy9IYTZWS25JWDNzc3R5ZTl6MEZLQWNEMEFJUjNueEpUNTdoR3I2M2lwSkdFU2NSNWEzWjkrZktSNnMzQW0yejJmM1N1ZVVVNEwya1BhbDF3TEE4STR3K1p2ZUxBSnhoWHAiLCJtYWMiOiIwZjEwOGExMTk3OWRjYWRiZmM4YThkMDhhZWFjODg2YTRhMmFkZmMyMDM0MTliYWMyMzA3OTA2YzI1ZjQwN2ZiIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"660 characters\">C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\app\\client\\egwun\\product\\12.1.0\\client_1\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PuTTY\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\.config\\herd-lite\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"62 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"94 characters\">&lt;address&gt;Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12 Server at localhost Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"39 characters\">C:/xampp/htdocs/klozza/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64990</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/klozza/public/_ignition/health-check</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/klozza/public/_ignition/health-check</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/klozza/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/klozza/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.8821</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629034456\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-87722467 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__clerk_db_jwt</span>\" => \"<span class=sf-dump-str title=\"31 characters\">dvb_2wprmNsbyLkorzu5rSid0IvWPQg</span>\"\n  \"<span class=sf-dump-key>__clerk_db_jwt_2UYcsRFZ</span>\" => \"<span class=sf-dump-str title=\"31 characters\">dvb_2wprmNsbyLkorzu5rSid0IvWPQg</span>\"\n  \"<span class=sf-dump-key>__refresh_2UYcsRFZ</span>\" => \"<span class=sf-dump-str title=\"20 characters\">rCX9HMTMzF9QXwDy1HwQ</span>\"\n  \"<span class=sf-dump-key>__session</span>\" => \"<span class=sf-dump-str title=\"784 characters\">eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.gMlP5bpPsF9RGYYHPqpZPdKMjX48ub7CTAL8fM7Q-6J2oJ4KZumNx5nS9cfbLcAKH8moi64uUoQcx97Dz1oUujg0IUFkINCBdDZBl0HZZZ4njPXYOtcKuJL9Cxc6qrAFDURt2De1ddeTiIN9sHv6MjGQC7DloeW4osjraD82gIxK3AC_nY9DvuvVL1HM4Z6kXKLUQMhNKiz_ZbYcUghN0XqPommeW5u1WbgRHO61-ff4j09Sy88mk9ibiptyBBm_AXnp7cb4fz9tU9ddmh4KlvcaVIqWPh1r4fvWkJLB6FR9LxBRSnSxcc9HKngi7bmmoWbuaJG4o9-dN6mzn8ea3w</span>\"\n  \"<span class=sf-dump-key>__client_uat_2UYcsRFZ</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1746877532</span>\"\n  \"<span class=sf-dump-key>__client_uat</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1746877532</span>\"\n  \"<span class=sf-dump-key>__session_2UYcsRFZ</span>\" => \"<span class=sf-dump-str title=\"799 characters\">eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJzcmYiOnRydWUsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.WXRZ7RQEUvPhycp89hRZRKiCRcxaGKJdpTkfAZKF0jKvnn6E9iUTJoOxFxXy8N_3thT5lSXhRD8feEqYUv0j6a6OPv3lDCPzlniS2A73uy3JasqJ-zent4jZlyHAdSkUrhC8Ez01YSm6UZJTtAZzNBtPw_-fcv21-FxfEYwR8BkdHLGpALpLrCcDHJ5-i1zUIjH8_ORuh0aS-N2cjLZEOJ6ehGNAEX15fiRogbHtItp8_0xK8He6-AKwL-vH3GlK1huFox1ByyDE8A7FPhXrGtsq5hy1a39czoFcqX-ZATp8f0XQmnoHeCH8DHY_eKYXt5vpUuIOgf-_3D4VW-7GLA</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkJaZTdjK1NreFQvdkNUSG85NTFITFE9PSIsInZhbHVlIjoicjV0QnBLSGVmVnY2OGcva2tqREtiVHE5dGJVc3BVd3NVYTJ0MWlucGhzRTNhRzdJRlFBSkZQdTV4L0htSzF2c0YyeGdtU0Y5clRtc0J5MklJRnlyN2RUR1RXZ1V2bFlQSWRiOEloYloxOXQ5MTcrcGNQRnFUdXZzMmdZdDdOcHEiLCJtYWMiOiI3YTY4Y2Q1MjM2MTM2MWQ0OTRlYTFjOTQyMzcxMmRhNmIxY2M0YmE3ZDQwZWM2MWQ0NTc0YTliNGRlNDA5MjA4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>klozza_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imx3NGV4K3l1VC9jNld0UWtVVTRNd3c9PSIsInZhbHVlIjoicEMyb1dVcDRvRWVoUWpTb1UrYllCaEdzUDdyVjU1Qy9IYTZWS25JWDNzc3R5ZTl6MEZLQWNEMEFJUjNueEpUNTdoR3I2M2lwSkdFU2NSNWEzWjkrZktSNnMzQW0yejJmM1N1ZVVVNEwya1BhbDF3TEE4STR3K1p2ZUxBSnhoWHAiLCJtYWMiOiIwZjEwOGExMTk3OWRjYWRiZmM4YThkMDhhZWFjODg2YTRhMmFkZmMyMDM0MTliYWMyMzA3OTA2YzI1ZjQwN2ZiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87722467\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-517028821 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 14:39:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517028821\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1202653884 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1202653884\", {\"maxDepth\":0})</script>\n"}}