{"__meta": {"id": "Xc59980e7167ce8598b2e1a9d9fa6963e", "datetime": "2025-06-10 16:57:48", "utime": 1749567468.870217, "method": "GET", "uri": "/klozza/public/", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 7, "messages": [{"message": "[16:57:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1749567467.127133, "collector": "log"}, {"message": "[16:57:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1749567467.127201, "collector": "log"}, {"message": "[16:57:47] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1749567467.141276, "collector": "log"}, {"message": "[16:57:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1749567467.154638, "collector": "log"}, {"message": "[16:57:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in C:\\xampp\\htdocs\\klozza\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1749567467.154774, "collector": "log"}, {"message": "[16:57:48] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\xampp\\htdocs\\klozza\\storage\\framework\\views\\0127f6518346d3e05ce38a2d7945e0278f269797.php on line 138", "message_html": null, "is_string": false, "label": "warning", "time": 1749567468.665724, "collector": "log"}, {"message": "[16:57:48] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\xampp\\htdocs\\klozza\\storage\\framework\\views\\7b36f96f1ea5ec3648421c75b392f5c26fb2c9ea.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": 1749567468.854317, "collector": "log"}]}, "time": {"start": 1749567466.621969, "end": 1749567468.87025, "duration": 2.2482810020446777, "duration_str": "2.25s", "measures": [{"label": "Booting", "start": 1749567466.621969, "relative_start": 0, "end": 1749567467.111332, "relative_end": 1749567467.111332, "duration": 0.4893629550933838, "duration_str": "489ms", "params": [], "collector": null}, {"label": "Application", "start": 1749567467.116424, "relative_start": 0.49445509910583496, "end": 1749567468.870252, "relative_end": 1.9073486328125e-06, "duration": 1.7538278102874756, "duration_str": "1.75s", "params": [], "collector": null}]}, "memory": {"peak_usage": 28095880, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "restorants.index (\\resources\\views\\restorants\\index.blade.php)", "param_count": 4, "params": ["parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants"], "type": "blade"}, {"name": "restorants.partials.modals (\\resources\\views\\restorants\\partials\\modals.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants"], "type": "blade"}, {"name": "partials.flash (\\resources\\views\\partials\\flash.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.footers.auth (\\resources\\views\\layouts\\footers\\auth.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant"], "type": "blade"}, {"name": "layouts.footers.nav (\\resources\\views\\layouts\\footers\\nav.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title"], "type": "blade"}, {"name": "laravelpwa::meta (\\resources\\views\\vendor\\laravelpwa\\meta.blade.php)", "param_count": 1, "params": ["config"], "type": "blade"}, {"name": "layouts.rtl (\\resources\\views\\layouts\\rtl.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title", "config"], "type": "blade"}, {"name": "layouts.common (\\resources\\views\\layouts\\common.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title", "config"], "type": "blade"}, {"name": "layouts.navbars.sidebar (\\resources\\views\\layouts\\navbars\\sidebar.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title", "config"], "type": "blade"}, {"name": "layouts.navbars.menus.admin (\\resources\\views\\layouts\\navbars\\menus\\admin.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title", "config"], "type": "blade"}, {"name": "layouts.navbars.navbar (\\resources\\views\\layouts\\navbars\\navbar.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title", "config"], "type": "blade"}, {"name": "layouts.navbars.navs.auth (\\resources\\views\\layouts\\navbars\\navs\\auth.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "parameters", "<PERSON><PERSON><PERSON><PERSON>", "allRes", "restorants", "__currentLoopData", "res", "key", "loop", "restorant", "title", "config"], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web, auth, impersonate", "as": "admin.restaurants.index", "controller": "App\\Http\\Controllers\\RestorantController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\klozza\\app\\Http\\Controllers\\RestorantController.php&line=63\">\\app\\Http\\Controllers\\RestorantController.php:63-101</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.024080000000000004, "accumulated_duration_str": "24.08ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "klo<PERSON>"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'klozza' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["klo<PERSON>", "roles"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 42}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 185}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\RestorantController.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.01657, "duration_str": "16.57ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "klo<PERSON>"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\User'", "type": "query", "params": [], "bindings": ["1", "App\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 185}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\RestorantController.php", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:185", "connection": "klo<PERSON>"}, {"sql": "select `name`, `id` from `companies` where `companies`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\RestorantController.php", "line": 85}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\RestorantController.php:85", "connection": "klo<PERSON>"}, {"sql": "select count(*) as aggregate from `companies` where `companies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\RestorantController.php", "line": 90}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Controllers\\RestorantController.php:90", "connection": "klo<PERSON>"}, {"sql": "select * from `companies` where `companies`.`deleted_at` is null order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\RestorantController.php", "line": 90}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\RestorantController.php:90", "connection": "klo<PERSON>"}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "42320b042c37d2f2cdadf05745a225b00f169173", "line": 81}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 25, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "view::42320b042c37d2f2cdadf05745a225b00f169173:81", "connection": "klo<PERSON>"}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "42320b042c37d2f2cdadf05745a225b00f169173", "line": 81}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 25, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "view::42320b042c37d2f2cdadf05745a225b00f169173:81", "connection": "klo<PERSON>"}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "42320b042c37d2f2cdadf05745a225b00f169173", "line": 81}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 25, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "view::42320b042c37d2f2cdadf05745a225b00f169173:81", "connection": "klo<PERSON>"}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "42320b042c37d2f2cdadf05745a225b00f169173", "line": 81}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 25, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "view::42320b042c37d2f2cdadf05745a225b00f169173:81", "connection": "klo<PERSON>"}]}, "models": {"data": {"App\\Restorant": 4, "Spatie\\Permission\\Models\\Role": 1, "App\\User": 5}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "F7tUhLMmoVTUbMnNBNcnREqxTKvjbmFOj7lbT5wR", "_previous": "array:1 [\n  \"url\" => \"http://localhost/klozza/public\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "restaurant_id": "2", "restaurant_currency": "null", "restaurant_convertion": "null", "auth": "array:1 [\n  \"password_confirmed_at\" => 1749567466\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-19733780 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-19733780\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1765641620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1765641620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-284836050 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-284836050\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-131534189 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/klozza/public/admin-login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2533 characters\">__clerk_db_jwt=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __clerk_db_jwt_2UYcsRFZ=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __refresh_2UYcsRFZ=rCX9HMTMzF9QXwDy1HwQ; __session=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.gMlP5bpPsF9RGYYHPqpZPdKMjX48ub7CTAL8fM7Q-6J2oJ4KZumNx5nS9cfbLcAKH8moi64uUoQcx97Dz1oUujg0IUFkINCBdDZBl0HZZZ4njPXYOtcKuJL9Cxc6qrAFDURt2De1ddeTiIN9sHv6MjGQC7DloeW4osjraD82gIxK3AC_nY9DvuvVL1HM4Z6kXKLUQMhNKiz_ZbYcUghN0XqPommeW5u1WbgRHO61-ff4j09Sy88mk9ibiptyBBm_AXnp7cb4fz9tU9ddmh4KlvcaVIqWPh1r4fvWkJLB6FR9LxBRSnSxcc9HKngi7bmmoWbuaJG4o9-dN6mzn8ea3w; __client_uat_2UYcsRFZ=1746877532; __client_uat=1746877532; __session_2UYcsRFZ=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJzcmYiOnRydWUsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.WXRZ7RQEUvPhycp89hRZRKiCRcxaGKJdpTkfAZKF0jKvnn6E9iUTJoOxFxXy8N_3thT5lSXhRD8feEqYUv0j6a6OPv3lDCPzlniS2A73uy3JasqJ-zent4jZlyHAdSkUrhC8Ez01YSm6UZJTtAZzNBtPw_-fcv21-FxfEYwR8BkdHLGpALpLrCcDHJ5-i1zUIjH8_ORuh0aS-N2cjLZEOJ6ehGNAEX15fiRogbHtItp8_0xK8He6-AKwL-vH3GlK1huFox1ByyDE8A7FPhXrGtsq5hy1a39czoFcqX-ZATp8f0XQmnoHeCH8DHY_eKYXt5vpUuIOgf-_3D4VW-7GLA; XSRF-TOKEN=eyJpdiI6IkFicjRpMVY0YzB6UU1BTnREazdvWkE9PSIsInZhbHVlIjoiZFF2VVJ1TWJCWjdkdCtwby9LNHFqVGd0N1orNnFmdVZUK2VFS2VCWmdtMXJlcllTbW5NTXdrUUdLY3JTQzZWS3VjNUVhYVgyZ3owNDB1M0JVWXp5cjh5cHJqRm1kVUROeFlFdXA4cmRIcWk5SWlUUHBYQmtUMTU3OEk2SktJcDgiLCJtYWMiOiIyMjZlOTY2YThhZDgxZjJjYWY2ZjRmMjFhMmMzMGY0YWMyNTdhNWM3MTRkMjFmZDY0NWZlODE5NjFhYWU1NjM4IiwidGFnIjoiIn0%3D; klozza_session=eyJpdiI6IjZCMGtFWThibmovdXF4U0JSUnZjM2c9PSIsInZhbHVlIjoib3lVbGErOENCUHVhczRYaUZvcjRxQTFBUXNSMEF5TlI3YmJhNUx0YkFJZk5XdEVPOWJtUi82SktuT2dSVDIrNkVMZ2hBaDhPTkR4ZUVQcHJvMlo2UXJhOC9rTDRoNnhCWFdHMGR6ZHFXQStQc011R1E0K1V5Q25XdkJYUEJ1UU8iLCJtYWMiOiJjYTJkYTFiNjY0YmQ2YzM1YjBmYTJhOWY2ZDBmNTc0MWMyNmZmODE3ODlhY2U0MmM4YTAzM2ExMDRmMzQwN2FmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131534189\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1842862598 data-indent-pad=\"  \"><span class=sf-dump-note>array:50</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost/klozza/public/admin-login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2533 characters\">__clerk_db_jwt=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __clerk_db_jwt_2UYcsRFZ=dvb_2wprmNsbyLkorzu5rSid0IvWPQg; __refresh_2UYcsRFZ=rCX9HMTMzF9QXwDy1HwQ; __session=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.gMlP5bpPsF9RGYYHPqpZPdKMjX48ub7CTAL8fM7Q-6J2oJ4KZumNx5nS9cfbLcAKH8moi64uUoQcx97Dz1oUujg0IUFkINCBdDZBl0HZZZ4njPXYOtcKuJL9Cxc6qrAFDURt2De1ddeTiIN9sHv6MjGQC7DloeW4osjraD82gIxK3AC_nY9DvuvVL1HM4Z6kXKLUQMhNKiz_ZbYcUghN0XqPommeW5u1WbgRHO61-ff4j09Sy88mk9ibiptyBBm_AXnp7cb4fz9tU9ddmh4KlvcaVIqWPh1r4fvWkJLB6FR9LxBRSnSxcc9HKngi7bmmoWbuaJG4o9-dN6mzn8ea3w; __client_uat_2UYcsRFZ=1746877532; __client_uat=1746877532; __session_2UYcsRFZ=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3BwakFTVE95U2Y1SlJjMWs0bnVXQ3lYaFMiLCJzcmYiOnRydWUsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.WXRZ7RQEUvPhycp89hRZRKiCRcxaGKJdpTkfAZKF0jKvnn6E9iUTJoOxFxXy8N_3thT5lSXhRD8feEqYUv0j6a6OPv3lDCPzlniS2A73uy3JasqJ-zent4jZlyHAdSkUrhC8Ez01YSm6UZJTtAZzNBtPw_-fcv21-FxfEYwR8BkdHLGpALpLrCcDHJ5-i1zUIjH8_ORuh0aS-N2cjLZEOJ6ehGNAEX15fiRogbHtItp8_0xK8He6-AKwL-vH3GlK1huFox1ByyDE8A7FPhXrGtsq5hy1a39czoFcqX-ZATp8f0XQmnoHeCH8DHY_eKYXt5vpUuIOgf-_3D4VW-7GLA; XSRF-TOKEN=eyJpdiI6IkFicjRpMVY0YzB6UU1BTnREazdvWkE9PSIsInZhbHVlIjoiZFF2VVJ1TWJCWjdkdCtwby9LNHFqVGd0N1orNnFmdVZUK2VFS2VCWmdtMXJlcllTbW5NTXdrUUdLY3JTQzZWS3VjNUVhYVgyZ3owNDB1M0JVWXp5cjh5cHJqRm1kVUROeFlFdXA4cmRIcWk5SWlUUHBYQmtUMTU3OEk2SktJcDgiLCJtYWMiOiIyMjZlOTY2YThhZDgxZjJjYWY2ZjRmMjFhMmMzMGY0YWMyNTdhNWM3MTRkMjFmZDY0NWZlODE5NjFhYWU1NjM4IiwidGFnIjoiIn0%3D; klozza_session=eyJpdiI6IjZCMGtFWThibmovdXF4U0JSUnZjM2c9PSIsInZhbHVlIjoib3lVbGErOENCUHVhczRYaUZvcjRxQTFBUXNSMEF5TlI3YmJhNUx0YkFJZk5XdEVPOWJtUi82SktuT2dSVDIrNkVMZ2hBaDhPTkR4ZUVQcHJvMlo2UXJhOC9rTDRoNnhCWFdHMGR6ZHFXQStQc011R1E0K1V5Q25XdkJYUEJ1UU8iLCJtYWMiOiJjYTJkYTFiNjY0YmQ2YzM1YjBmYTJhOWY2ZDBmNTc0MWMyNmZmODE3ODlhY2U0MmM4YTAzM2ExMDRmMzQwN2FmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"660 characters\">C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\app\\client\\egwun\\product\\12.1.0\\client_1\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PuTTY\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\.config\\herd-lite\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"62 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"94 characters\">&lt;address&gt;Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12 Server at localhost Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"39 characters\">C:/xampp/htdocs/klozza/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">65492</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/klozza/public/</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/klozza/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/klozza/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1749567466.622</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1749567466</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842862598\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-925023740 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__clerk_db_jwt</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__clerk_db_jwt_2UYcsRFZ</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__refresh_2UYcsRFZ</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat_2UYcsRFZ</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__client_uat</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__session_2UYcsRFZ</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F7tUhLMmoVTUbMnNBNcnREqxTKvjbmFOj7lbT5wR</span>\"\n  \"<span class=sf-dump-key>klozza_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mVu6w9W3cqQz6MDwied58i0dN0HCngndkGz0wVVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925023740\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1122608717 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 10 Jun 2025 14:57:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhVczgySzVGUk40QW5zQ0IrcE5wS1E9PSIsInZhbHVlIjoiVTVsclhJczhlSkNqdGVhakRaSDdadFVTc0FxdGNJVGJoZUlvSVJNak5zMkNJbHQ1TU9FR05nRE8zZmxxN3JYWkhlSVpRTjhEeDNUZDN5U1Y5bHdPK040ZjJId1VxV1ZWT0xXS0dhbWlBdS8xSytMb2JndzBTMGVqeVdRSm5KcUoiLCJtYWMiOiIzMDgwMzUyMzRkZGRlMjFhZjY5YTIyNTI3ODM1ZDM1ZDFiYTBkMmUxNWJhZTUyMjBiNTBkMWE1YTA2MzA0ZjVkIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 16:57:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">klozza_session=eyJpdiI6IlFBdlBEMlhiZjRBZzk4OGhYMlF4WVE9PSIsInZhbHVlIjoiTHZOaVdpTncwUHpuZmRiSlBKU2ROTUc2TUliSEFVY21xbGFuL0t4NFVLdU1GTjhaRG1IZURTMTd6UkVDS2NBQzh6VEZ4VlZRaWl2MFB2c01FbmNISTdGVWlSRGsrbmg3VWE1dDl5c2JsNlZYc3UyakdUdWIzZnhJL1pJQi9tcUIiLCJtYWMiOiIyNGE3ZDYyMTQzNDE2NDZkZWRjNWEyOGMxOTJmZTA3MjE1MDdhNWNhZTMwNjk1MzU5ZmIxNTc3ZmRkMDE2NjVkIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 16:57:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhVczgySzVGUk40QW5zQ0IrcE5wS1E9PSIsInZhbHVlIjoiVTVsclhJczhlSkNqdGVhakRaSDdadFVTc0FxdGNJVGJoZUlvSVJNak5zMkNJbHQ1TU9FR05nRE8zZmxxN3JYWkhlSVpRTjhEeDNUZDN5U1Y5bHdPK040ZjJId1VxV1ZWT0xXS0dhbWlBdS8xSytMb2JndzBTMGVqeVdRSm5KcUoiLCJtYWMiOiIzMDgwMzUyMzRkZGRlMjFhZjY5YTIyNTI3ODM1ZDM1ZDFiYTBkMmUxNWJhZTUyMjBiNTBkMWE1YTA2MzA0ZjVkIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 16:57:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">klozza_session=eyJpdiI6IlFBdlBEMlhiZjRBZzk4OGhYMlF4WVE9PSIsInZhbHVlIjoiTHZOaVdpTncwUHpuZmRiSlBKU2ROTUc2TUliSEFVY21xbGFuL0t4NFVLdU1GTjhaRG1IZURTMTd6UkVDS2NBQzh6VEZ4VlZRaWl2MFB2c01FbmNISTdGVWlSRGsrbmg3VWE1dDl5c2JsNlZYc3UyakdUdWIzZnhJL1pJQi9tcUIiLCJtYWMiOiIyNGE3ZDYyMTQzNDE2NDZkZWRjNWEyOGMxOTJmZTA3MjE1MDdhNWNhZTMwNjk1MzU5ZmIxNTc3ZmRkMDE2NjVkIiwidGFnIjoiIn0%3D; expires=Tue, 10-Jun-2025 16:57:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122608717\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-995844408 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F7tUhLMmoVTUbMnNBNcnREqxTKvjbmFOj7lbT5wR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/klozza/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>restaurant_id</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>restaurant_currency</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>restaurant_convertion</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749567466</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995844408\", {\"maxDepth\":0})</script>\n"}}