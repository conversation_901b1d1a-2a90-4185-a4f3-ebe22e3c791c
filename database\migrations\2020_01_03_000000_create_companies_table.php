<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCompaniesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('logo')->nullable();
            $table->unsignedBigInteger('city_id')->nullable();
            $table->string('subdomain')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->decimal('lat', 10, 8)->nullable();
            $table->decimal('lng', 11, 8)->nullable();
            $table->integer('radius')->nullable();
            $table->string('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('whatsapp_phone')->nullable();
            $table->text('description')->nullable();
            $table->decimal('minimum', 8, 2)->default(0);
            $table->text('payment_info')->nullable();
            $table->string('mollie_payment_key')->nullable();
            $table->boolean('can_pickup')->default(1);
            $table->boolean('can_delivery')->default(1);
            $table->boolean('can_dinein')->default(0);
            $table->decimal('fee', 8, 2)->default(0);
            $table->decimal('static_fee', 8, 2)->default(0);
            $table->boolean('is_featured')->default(0);
            $table->boolean('active')->default(1);
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('city_id')->references('id')->on('cities');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('companies');
    }
}
