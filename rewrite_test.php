<?php
echo "<h2>Apache Module Test</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✅ mod_rewrite is ENABLED</p>";
    } else {
        echo "<p style='color: red;'>❌ mod_rewrite is DISABLED</p>";
    }
    echo "<h3>All loaded modules:</h3>";
    echo "<pre>" . print_r($modules, true) . "</pre>";
} else {
    echo "<p style='color: orange;'>⚠️ Cannot determine Apache modules (function not available)</p>";
}

echo "<h3>Server Info:</h3>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
?>
